-- 1. Create reviews table
CREATE TABLE `reviews` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `surname` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `company` varchar(255) DEFAULT NULL,
  `job_title` varchar(255) DEFAULT NULL,
  `message` text DEFAULT NULL,
  `review_text` text DEFAULT NULL,
  `rating` int(11) DEFAULT NULL,
  `model_type` varchar(255) DEFAULT NULL,
  `model_id` bigint(20) unsigned DEFAULT NULL,
  `type` varchar(255) DEFAULT NULL,
  `award_id` bigint(20) unsigned DEFAULT NULL,
  `award_title` varchar(255) DEFAULT NULL,
  `status` enum('pending','approved','rejected') DEFAULT 'pending',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  <PERSON><PERSON>AR<PERSON>Y (`id`),
  <PERSON><PERSON>Y `reviews_model_type_model_id_index` (`model_type`,`model_id`),
  KEY `reviews_award_id_index` (`award_id`),
  KEY `reviews_status_index` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. Add Reviews to admin panel
INSERT INTO `shuttle_scaffold_interfaces` (`id`, `name`, `icon`, `slug`, `display_name_singular`, `display_name_plural`, `migration`, `model`, `translation_model`, `controller`, `menuable`, `by_shuttle`, `created_at`, `updated_at`) VALUES
(20, 'reviews', 'iconsmind-Star', 'reviews', 'Review', 'Reviews', NULL, 'App\\Models\\Review', NULL, 'App\\Http\\Controllers\\Shuttle\\ReviewsController', 1, 0, NOW(), NOW());

-- 3. Add scaffold interface rows for Reviews
INSERT INTO `shuttle_scaffold_interface_rows` (`rowable_type`, `rowable_id`, `field`, `type`, `display_name`, `required`, `browse`, `read`, `edit`, `add`, `delete`, `details`, `ord`, `parent_id`) VALUES
('Sina\\Shuttle\\Models\\ScaffoldInterface', 20, 'id', 'number', 'ID', 0, 1, 1, 0, 0, 0, '{}', 1, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', 20, 'name', 'text', 'Name', 1, 1, 1, 1, 1, 0, '{}', 2, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', 20, 'surname', 'text', 'Surname', 1, 1, 1, 1, 1, 0, '{}', 3, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', 20, 'email', 'text', 'Email', 1, 1, 1, 1, 1, 0, '{}', 4, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', 20, 'phone', 'text', 'Phone', 0, 1, 1, 1, 1, 0, '{}', 5, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', 20, 'company', 'text', 'Company', 0, 1, 1, 1, 1, 0, '{}', 6, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', 20, 'job_title', 'text', 'Job Title', 0, 1, 1, 1, 1, 0, '{}', 7, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', 20, 'message', 'text_area', 'Message', 0, 0, 1, 1, 1, 0, '{}', 8, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', 20, 'review_text', 'text_area', 'Review Text', 0, 1, 1, 1, 1, 0, '{}', 9, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', 20, 'rating', 'number', 'Rating', 0, 1, 1, 1, 1, 0, '{}', 10, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', 20, 'award_title', 'text', 'Award Title', 0, 1, 1, 1, 1, 0, '{}', 11, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', 20, 'status', 'select_dropdown', 'Status', 1, 1, 1, 1, 1, 0, '{"default":"pending","options":{"pending":"Pending","approved":"Approved","rejected":"Rejected"}}', 12, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', 20, 'created_at', 'timestamp', 'Created At', 0, 1, 1, 0, 0, 0, '{}', 13, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', 20, 'updated_at', 'timestamp', 'Updated At', 0, 0, 0, 0, 0, 0, '{}', 14, 0);

-- 4. Add Reviews to main menu
INSERT INTO `shuttle_menu_items` (`menu_id`, `title`, `url`, `target`, `icon_class`, `color`, `parent_id`, `ord`, `created_at`, `updated_at`, `menuable_type`, `menuable_id`, `lft`, `rgt`, `depth`) VALUES
(1, 'Reviews', NULL, '_self', 'iconsmind-Star', NULL, 0, 999, NOW(), NOW(), 'Sina\\Shuttle\\Models\\ScaffoldInterface', 20, 999, 1000, 0);
