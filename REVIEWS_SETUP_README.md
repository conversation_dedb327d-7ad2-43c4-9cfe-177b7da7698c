# Reviews Functionality Setup

## Overview
ეს ფუნქციონალი ამატებს "Review Us" სექციას awards-ის enquiry მოდალში და ქმნის admin panel-ში Reviews განყოფილებას.

## Files Created/Modified

### 1. Database & Models
- `setup_reviews.sql` - SQL ფაილი reviews ცხრილისა და admin panel კონფიგურაციისთვის
- `app/Models/Review.php` - Review Model

### 2. Controllers
- `app/Http/Controllers/ContactController.php` - განახლებული review მონაცემების შესანახად
- `app/Http/Controllers/Shuttle/ReviewsController.php` - Admin panel controller

### 3. Views
- `resources/views/awards/show.blade.php` - განახლებული Review Us სექციით
- `resources/views/shuttle/reviews/index.blade.php` - Admin reviews list
- `resources/views/shuttle/reviews/edit_add.blade.php` - Admin review edit/add
- `resources/views/shuttle/reviews/show.blade.php` - Admin review details

## Installation Steps

### 1. Execute SQL
```bash
mysql -u root -p your_database_name < setup_reviews.sql
```

### 2. Features Added

#### Frontend (Awards Page)
- Awards-ის enquiry მოდალში დამატებულია "Review Us" სექცია
- მომხმარებელს შეუძლია დატოვოს review text
- ყველა review ინახება `reviews` ცხრილში

#### Admin Panel
- ახალი "Reviews" განყოფილება admin მენიუში
- Reviews-ის სია status filter-ით (Pending, Approved, Rejected)
- Review-ის დეტალური ნახვა და რედაქტირება
- Status-ის შეცვლის შესაძლებლობა

### 3. Database Structure
```sql
reviews table:
- id, name, surname, email, phone, company, job_title
- message, review_text, rating
- model_type, model_id (polymorphic relation)
- award_id, award_title
- status (pending/approved/rejected)
- timestamps
```

### 4. How It Works
1. მომხმარებელი awards გვერდზე ავსებს enquiry ფორმას review text-ით
2. ContactController ინახავს როგორც enquiry-ს, ასევე review-ს
3. Admin panel-ში ჩანს ყველა review status-ით
4. Admin-ს შეუძლია status-ის შეცვლა (pending → approved/rejected)

### 5. Admin Access
- URL: `/mypanel/reviews`
- მენიუში: "Reviews" (ვარსკვლავის აიკონით)

## Notes
- Reviews ქმნება მხოლოდ frontend-იდან (awards enquiry)
- Admin panel-ში მხოლოდ ნახვა/რედაქტირება/წაშლა
- ყველა review იწყება "pending" status-ით
- მიგრაციას არ ვიყენებთ - მხოლოდ SQL ფაილი
