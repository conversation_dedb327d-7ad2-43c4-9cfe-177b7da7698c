<?php

use Illuminate\Http\Request;
use Sina\Shuttle\Facades\Shuttle;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\CartController;
use App\Http\Controllers\LoginController;
use App\Http\Controllers\CourseController;
use App\Http\Controllers\AwwardsController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\PackageController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\RegisterController;
use App\Http\Controllers\ConferenceController;
use App\Http\Controllers\FestivalController;
use App\Http\Controllers\SocialAuthController;
use App\Http\Controllers\TransactionController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\Shuttle\ExportController as ShuttleExportController;
use App\Http\Controllers\Shuttle\AwwardsController as ShuttleAwwardsController;
use App\Http\Controllers\Shuttle\ConferenceController as ShuttleConferenceController;
use App\Http\Controllers\Shuttle\FestivalController as ShuttleFestivalController;
use App\Http\Controllers\Shuttle\TransactionController as ShuttleTransactionController;
use App\Http\Controllers\SearchController;
use App\Models\Review;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('artisan', function () {
//    Artisan::call('storage:link');
//    $outputt = Artisan::output();
//    print $outputt;
    \Sina\Shuttle\Models\Admin::updateOrCreate([
        'email' => '<EMAIL>',],[
        'password' => bcrypt('Maka123')
    ]);
});

// Review show route for modal
Route::get('mypanel/scaffold-interface/{scaffoldInterface}/show/{id}', function ($scaffoldInterface, $id) {
    $review = Review::findOrFail($id);
    return response()->json($review);
});

Route::get('check-page-components/{pageId?}', function ($pageId = 22) {

    $page = \Sina\Shuttle\Models\Page::with(['components' => function($query) {
        $query->orderBy('position');
    }])->find($pageId);

    if (!$page) {
        return "Page with ID $pageId not found";
    }

    $html = "<h2>Page: {$page->title}</h2>";
    $html .= "<p>URL: {$page->url}</p>";
    $html .= "<p>Total Components: " . $page->components->count() . "</p>";

    // Check template file
    $templateFile = resource_path("views/sections/en/{$pageId}.blade.php");
    $html .= "<p>Template File: " . $templateFile . "</p>";
    $html .= "<p>Template Exists: " . (file_exists($templateFile) ? 'YES' : 'NO') . "</p>";
    if (file_exists($templateFile)) {
        $html .= "<p>Template Content:</p>";
        $html .= "<pre>" . htmlspecialchars(file_get_contents($templateFile)) . "</pre>";
    }

    if ($page->components->count() > 0) {
        $html .= "<h3>Components:</h3>";
        $html .= "<ul>";
        foreach ($page->components as $index => $component) {
            $html .= "<li>";
            $html .= "[$index] Component: <strong>{$component->name}</strong><br>";
            $html .= "Locale: {$component->pivot->locale}<br>";
            $html .= "Position: {$component->pivot->position}<br>";
            $html .= "Settings: " . json_encode($component->pivot->setting) . "<br>";
            $html .= "</li><br>";
        }
        $html .= "</ul>";

        // Test what should be in template
        $html .= "<h3>What Template Should Contain:</h3>";
        $html .= "<pre>";
        $html .= htmlspecialchars('@foreach($page->components as $component)') . "\n";
        $html .= htmlspecialchars('<x-shuttle-dynamic-component :name="$component->name" :c="$component" :data="$component->pivot->setting"></x-shuttle-dynamic-component>') . "\n";
        $html .= htmlspecialchars('@endforeach');
        $html .= "</pre>";

    } else {
        $html .= "<p>No components found for this page.</p>";
    }

    // Add manual template creation button
    $html .= "<hr><h3>Manual Actions:</h3>";
    $html .= "<a href='/fix-template/{$pageId}' style='background: #007cba; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>Fix Template File</a>";

    return $html;
});

Route::get('fix-template/{pageId}', function ($pageId) {
    try {
        $page = \Sina\Shuttle\Models\Page::with(['components'])->find($pageId);

        if (!$page) {
            return "Page with ID $pageId not found";
        }

        $sourceFile = resource_path("views/sections/en/");

        if(!is_dir($sourceFile)){
            mkdir($sourceFile, 0755, true);
        }

        // Generate universal template that works with any number of components
        $contents = '@foreach($page->components as $component)' . "\n";
        $contents .= '<x-shuttle-dynamic-component :name="$component->name" :c="$component" :data="$component->pivot->setting"></x-shuttle-dynamic-component>' . "\n";
        $contents .= '@endforeach';

        file_put_contents($sourceFile . $pageId . ".blade.php", $contents);

        return "Template file created successfully for page {$pageId}! <br><br><a href='/check-page-components/{$pageId}'>Check Again</a> | <a href='/{$page->url}'>View Page</a>";

    } catch (Exception $e) {
        return "Error: " . $e->getMessage();
    }
});

Route::get('debug-components', function () {
    $components = \Sina\Shuttle\Models\Component::all();

    $html = "<h2>All Components in Database:</h2>";
    $html .= "<ul>";
    foreach ($components as $component) {
        $html .= "<li>ID: {$component->id} - Name: {$component->name} - Display: {$component->display_name}</li>";
    }
    $html .= "</ul>";

    return $html;
});
// Test route შექმენით
Route::get('/test-mail', function() {
    try {
        Mail::raw('Test email', function($message) {
            $message->to('<EMAIL>')->subject('Test');
        });
        return 'Email sent successfully';
    } catch (\Exception $e) {
        return 'Error: ' . $e->getMessage();
    }
});
Route::get('/test-awward-transaction/{packageId}', function ($packageId) {
    // You might need to authenticate a user for this to work correctly
    // For a quick test, you can manually log in a user or create a temporary one
    $user = \App\Models\User::first(); // Or any existing user
    if (!$user) {
        return "Please create a user in your database to test this route.";
    }
    auth()->login($user);

    $package = \App\Models\AwwardPackage::find($packageId);

    if (!$package) {
        return "AwwardPackage with ID {$packageId} not found.";
    }

    $request = new Illuminate\Http\Request([
        'name' => 'Test User',
        'email' => '<EMAIL>',
        'mobile' => '1234567890',
        'company' => 'Test Company',
        'job' => 'Test Job',
        'address1' => '123 Test St',
        'address2' => '',
        'city' => 'Test City',
        'zip_code' => '12345',
        'country' => 'Test Country',
        'payment_method' => 'test', // A dummy payment method
    ]);

    $controller = new App\Http\Controllers\TransactionController(new App\Service\TransactionService()); // Instantiate with a dummy service
    return $controller->awward($request, $package);

})->name('test.awward.transaction');
Shuttle::routes();

Route::post('form/{form}/submit', [ContactController::class, 'form'])->name('form.store');
Route::post('contact/enquiry', [ContactController::class, 'enquiry'])->name('contact.enquiry');
Route::post('/contact', [ContactController::class, 'send'])->name('contact.send');

// Promo Code API routes
Route::post('api/promo-code/validate', function(\Illuminate\Http\Request $request) {
    try {
        \Log::info('Promo code validation request', $request->all());

        $request->validate([
            'code' => 'required|string',
            'amount' => 'required|numeric|min:0'
        ]);

        $promoCode = \App\Models\PromoCode::where('code', strtoupper($request->code))->first();

        if (!$promoCode) {
            return response()->json([
                'valid' => false,
                'message' => 'Promo code not found.'
            ], 404);
        }

        // Simple validation without calling model methods
        $isActive = $promoCode->is_active;
        $currentTime = \Carbon\Carbon::now('Asia/Tbilisi');
        $expiresAt = \Carbon\Carbon::parse($promoCode->expires_at, 'Asia/Tbilisi');
        $isNotExpired = $expiresAt > $currentTime;
        $hasUsageLeft = $promoCode->max_usage === null || $promoCode->usage_count < $promoCode->max_usage;

        \Log::info('Promo code validation details', [
            'code' => $promoCode->code,
            'expires_at' => $expiresAt->toDateTimeString(),
            'current_time' => $currentTime->toDateTimeString(),
            'is_active' => $isActive,
            'is_not_expired' => $isNotExpired,
            'has_usage_left' => $hasUsageLeft,
            'timezone' => 'Asia/Tbilisi'
        ]);

        if (!$isActive || !$isNotExpired || !$hasUsageLeft) {
            $message = 'Promo code is ';
            if (!$isActive) {
                $message .= 'inactive.';
            } elseif (!$isNotExpired) {
                $message .= 'expired.';
            } else {
                $message .= 'no longer available.';
            }

            return response()->json([
                'valid' => false,
                'message' => $message
            ], 400);
        }

        $discountAmount = ($request->amount * $promoCode->discount_percentage) / 100;
        $finalAmount = $request->amount - $discountAmount;

        \Log::info('Promo code calculation', [
            'original_amount' => $request->amount,
            'discount_percentage' => $promoCode->discount_percentage,
            'discount_amount' => $discountAmount,
            'final_amount' => $finalAmount
        ]);

        return response()->json([
            'valid' => true,
            'message' => "Promo code applied! {$promoCode->discount_percentage}% discount.",
            'discount_percentage' => $promoCode->discount_percentage,
            'discount_amount' => number_format($discountAmount, 2),
            'original_amount' => number_format($request->amount, 2),
            'final_amount' => number_format($finalAmount, 2),
            'code' => $promoCode->code
        ]);

    } catch (\Exception $e) {
        \Log::error('Promo code validation error', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);

        return response()->json([
            'valid' => false,
            'message' => 'Error validating promo code: ' . $e->getMessage()
        ], 500);
    }
})->name('promo-code.validate');

Route::post('api/promo-code/apply', [App\Http\Controllers\PromoCodeController::class, 'apply'])->name('promo-code.apply');
Route::post('transaction', [TransactionController::class, 'store'])->name('transaction.store');
Route::post('transaction/{package}/conference', [TransactionController::class, 'conference'])->name('transaction.conference');
Route::post('transaction/{package}/awward', [TransactionController::class, 'awward'])->name('transaction.awward');
Route::post('transaction/{package}/festival', [TransactionController::class, 'festival'])->name('transaction.festival');
Route::post('cart/{courseLocation}', [CartController::class, 'store'])->name('cart.store');
Route::put('cart/{cart}', [CartController::class, 'update'])->name('cart.update');
Route::delete('cart/{cart}', [CartController::class, 'destroy'])->name('cart.destroy');
Route::get('/auth/google', [SocialAuthController::class, 'redirectToGoogle'])->name('google.redirect');
Route::get('/auth/google/callback', [SocialAuthController::class, 'handleGoogleCallback'])->name('google.callback');

// JavaScript-based Google Sign-In (Plesk-friendly)
Route::post('/auth/google/token', [SocialAuthController::class, 'handleGoogleToken'])->name('google.token');

// Test POST route
Route::post('/test-post', function() {
    return response()->json(['message' => 'POST route works!', 'data' => request()->all()]);
});

// Simple Google token handler (without controller)
Route::post('/auth/google/simple', function() {
    try {
        $token = request()->input('token');

        if (!$token) {
            return response()->json(['error' => 'Token is required'], 400);
        }

        // Verify the token with Google using HTTP request
        $response = file_get_contents("https://oauth2.googleapis.com/tokeninfo?id_token=" . $token);
        $payload = json_decode($response, true);

        if (!$payload || isset($payload['error'])) {
            return response()->json(['error' => 'Invalid token'], 400);
        }

        // Verify the audience (client_id)
        if ($payload['aud'] !== env('GOOGLE_CLIENT_ID')) {
            return response()->json(['error' => 'Invalid client'], 400);
        }

        // Extract user information
        $googleId = $payload['sub'];
        $email = $payload['email'];
        $firstName = $payload['given_name'] ?? '';
        $lastName = $payload['family_name'] ?? '';
        $avatar = $payload['picture'] ?? '';

        // Check if user already exists
        $user = \App\Models\User::where('email', $email)->first();

        if (!$user) {
            // Create new user
            $user = \App\Models\User::create([
                'first_name' => $firstName,
                'last_name' => $lastName,
                'email' => $email,
                'email_verified_at' => now(),
                'image' => $avatar,
                'password' => bcrypt(\Illuminate\Support\Str::random(16)),
                'google_id' => $googleId,
            ]);
        }

        \Illuminate\Support\Facades\Auth::login($user);

        return response()->json([
            'success' => true,
            'redirect' => '/profile',
            'user' => [
                'name' => $user->first_name . ' ' . $user->last_name,
                'email' => $user->email
            ]
        ]);

    } catch (\Exception $e) {
        return response()->json(['error' => 'Authentication failed: ' . $e->getMessage()], 500);
    }
});
Route::get('/google-signin-test', function() {
    return view('google-signin');
});

// Test route to check if callback route is accessible
Route::get('/test-google-callback', function() {
    return "✅ Google callback route is accessible! Current time: " . now();
});

// Alternative Google callback route (in case /auth/google/callback is blocked)
Route::get('/google-auth-callback', [SocialAuthController::class, 'handleGoogleCallback'])->name('google.callback.alt');

// Simple callback route for testing
Route::get('/oauth/google/return', [SocialAuthController::class, 'handleGoogleCallback'])->name('google.return');

// Test route to check if oauth path works
Route::get('/oauth/test', function() {
    return "OAuth test route works! Time: " . now();
});

// Even simpler test
Route::get('/google-login-success', function() {
    return "Google callback reached successfully! Parameters: " . json_encode(request()->all());
});

// Debug route to check server permissions
Route::get('/debug-server-access', function() {
    $output = "<h2>Server Access Debug</h2>";
    $output .= "Current URL: " . request()->fullUrl() . "<br>";
    $output .= "Server Software: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "<br>";
    $output .= "PHP Version: " . phpversion() . "<br>";
    $output .= "Laravel Version: " . app()->version() . "<br>";

    $output .= "<h3>Google OAuth Debug:</h3>";
    $output .= "Expected Redirect URI: " . config('services.google.redirect') . "<br>";
    $output .= "Google Client ID: " . (config('services.google.client_id') ? substr(config('services.google.client_id'), 0, 20) . '...' : 'NOT SET') . "<br>";

    $output .= "<h3>Test Routes:</h3>";
    $output .= "<a href='/test-google-callback'>Test Simple Route</a><br>";
    $output .= "<a href='/google-login-success'>Test Google Success Route</a><br>";
    $output .= "<a href='/oauth/google/return'>Test OAuth Return Route</a><br>";
    $output .= "<a href='/auth/google'>Test Google OAuth (will show redirect URI)</a><br>";

    return $output;
});

// Route to show exact Google OAuth URL being generated
Route::get('/show-google-oauth-url', function() {
    try {
        $googleUrl = Socialite::driver('google')
            ->stateless()
            ->scopes(['openid', 'profile', 'email'])
            ->getTargetUrl();

        return "<h2>Google OAuth URL Debug</h2>" .
               "<p><strong>Generated URL:</strong></p>" .
               "<textarea style='width:100%;height:100px;'>" . $googleUrl . "</textarea>" .
               "<p><strong>Redirect URI in URL:</strong> " .
               (parse_url($googleUrl, PHP_URL_QUERY) ?
                parse_str(parse_url($googleUrl, PHP_URL_QUERY), $params) . ($params['redirect_uri'] ?? 'Not found') :
                'Could not parse') . "</p>" .
               "<p><a href='/debug-server-access'>Back to Debug</a></p>";
    } catch (\Exception $e) {
        return "Error: " . $e->getMessage();
    }
});
Route::get('auth/linkedin', [SocialAuthController::class, 'redirectToLinkedIn'])->name('linkedin.redirect');
Route::get('auth/linkedin/callback', [SocialAuthController::class, 'handleLinkedInCallback']);
Shuttle::translatedGroup(function () {

    Route::get('cart', [CartController::class, 'index'])->name('cart.index');
    Route::get('courses', [CourseController::class, 'index'])->name('course.index');
    Route::get('courses/{course_title}', [CourseController::class, 'show'])->name('course.show');
    Route::get('conferences', [ConferenceController::class, 'index'])->name('conference.index');
    Route::get('conferences/{conference_title}', [ConferenceController::class, 'show'])->name('conference.show');
    Route::get('festivals', [FestivalController::class, 'index'])->name('festival.index');
    Route::get('festivals/{festival_title}', [FestivalController::class, 'show'])->name('festival.show');
    Route::get('awards', [AwwardsController::class, 'index'])->name('awward.index');
    Route::get('awards/{awward_title}', [AwwardsController::class, 'show'])->name('awward.show');
    Route::get('blog', [BlogController::class, 'index'])->name('blog.index');
    Route::get('blog/{slug}', [BlogController::class, 'show'])->name('blog.show')->where('slug', '.*');




    Route::middleware('guest')->group(function(){
        Route::view('register', 'auth.register')->name('register');
        Route::post('register', [RegisterController::class, 'store'])->name('register.store');
        Route::view('login', 'auth.login')->name('login');
        Route::post('login', [LoginController::class, 'store'])->name('login.store');
    });

    Route::middleware('auth')->group(function(){
        Route::get('profile/{tab?}', [ProfileController::class, 'index'])->name('profile');
        Route::put('profile', [ProfileController::class, 'update'])->name('profile.update');
        Route::get('logout', [ProfileController::class, 'logout'])->name('logout');
        Route::get('package/{package}/checkout', [PackageController::class, 'checkout'])->name('package.checkout');
        Route::post('package/{package}/transaction', [PackageController::class, 'transaction'])->name('package.transaction');
        Route::get('payment/redirect', [TransactionController::class, 'redirect'])->name('payment.redirect');
        Route::get('invoice/{transaction}', [TransactionController::class, 'invoice'])->name('transaction.invoice');
        Route::get('payment/{transaction}/success', [TransactionController::class, 'success'])->name('transaction.success');
    });
});

Shuttle::group(function ()
{
    Route::get('exports/users/download',  [ShuttleExportController::class, 'users'])->name('export.users');
    Route::get('exports/enquiries/download',  [ShuttleExportController::class, 'enquiries'])->name('export.enquiries');

    // Promo Codes routes
    Route::post('promo-codes/generate', [App\Http\Controllers\Shuttle\PromoCodeController::class, 'generate'])->name('promo-codes.generate');
    Route::get('promo-codes/{promoCode}/toggle', [App\Http\Controllers\Shuttle\PromoCodeController::class, 'toggle'])->name('promo-codes.toggle');
    Route::post('conference/{conference}/component',  [ShuttleConferenceController::class, 'addComponent'])->name('conference.component');
    Route::delete('conference/{component}/component',  [ShuttleConferenceController::class, 'destroyComponent'])->name('conference.component.destroy');
    Route::get('conference/{conference}/copy',  [ShuttleConferenceController::class, 'copy'])->name('conference.copy');


	 Route::post('awwards/{awwards}/component',  [ShuttleAwwardsController::class, 'addComponent'])->name('awwards.component');
    Route::delete('awwards/component/{component}',  [ShuttleAwwardsController::class, 'destroyComponent'])->name('awwards.component.destroy');
    Route::get('awwards/{awwards}/copy',  [ShuttleAwwardsController::class, 'copy'])->name('awwards.copy');

     Route::post('festival/{festival}/component',  [ShuttleFestivalController::class, 'addComponent'])->name('festival.component');
    Route::delete('festival/{festival}/component',  [ShuttleFestivalController::class, 'destroyComponent'])->name('festival.component.destroy');
    Route::get('festival/{festival}/copy',  [ShuttleFestivalController::class, 'copy'])->name('festival.copy');

});

// Add this temporary route to fix all blog slugs
Route::get('/fix-all-blog-slugs', function() {
    $blogs = \App\Models\Blog::all();
    $fixed = 0;
    
    foreach ($blogs as $blog) {
        $originalSlug = $blog->slug;
        
        // Clean up any existing slug
        if (!empty($blog->slug)) {
            // Remove any ":1" suffix
            $cleanSlug = preg_replace('/:1$/', '', $blog->slug);
            $blog->slug = $cleanSlug;
        } else {
            // Generate slug from title if missing
            $blog->slug = \Illuminate\Support\Str::slug($blog->title);
        }
        
        if ($originalSlug !== $blog->slug) {
            $blog->save();
            $fixed++;
        }
    }
    
    return "Fixed {$fixed} blog slugs!";
});

// Add this temporary route to test without middleware
Route::get('/test-blog/{slug}', function($slug) {
    if (is_numeric($slug)) {
        $post = \App\Models\Blog::findOrFail($slug);
    } else {
        $post = \App\Models\Blog::where('slug', $slug)->firstOrFail();
    }
    
    return [
        'id' => $post->id,
        'title' => $post->title,
        'slug' => $post->slug
    ];
})->withoutMiddleware(['web']); // Specify which middleware to exclude

// Add this route to check URL format
Route::get('/check-url', function() {
    $slug = 'why-attend-sharp-festival-of-marketing-pr-and-communications';
    $blog = \App\Models\Blog::where('slug', $slug)->first();
    
    if (!$blog) {
        return "Blog not found with slug: {$slug}";
    }
    
    return [
        'blog_id' => $blog->id,
        'blog_title' => $blog->title,
        'blog_slug' => $blog->slug,
        'url_with_route' => route('blog.show', $blog->slug),
        'direct_url' => url('/blog/' . $blog->slug),
    ];
});

// Add this route at the top level, outside of any groups
Route::get('/direct-blog-test/{slug}', [BlogController::class, 'show'])->name('direct.blog.test');

// Add this route to clear caches
Route::get('/clear-caches', function() {
    \Artisan::call('route:clear');
    \Artisan::call('cache:clear');
    \Artisan::call('config:clear');
    \Artisan::call('view:clear');

    return "All caches cleared!";
});

// Check database for awward relationship field
Route::get('/check-database', function() {
    try {
        // Check if the relationship field exists
        $result = DB::table('shuttle_scaffold_interface_rows')
            ->where('rowable_id', 16)
            ->where('field', 'awward_hasmany_awward_package_relationship')
            ->first();

        $output = "<h2>Database Check Results:</h2>";

        if ($result) {
            $output .= "✅ SUCCESS: awward_hasmany_awward_package_relationship field found!<br>";
            $output .= "Field details:<br>";
            $output .= "<pre>" . print_r($result, true) . "</pre>";
        } else {
            $output .= "❌ ERROR: awward_hasmany_awward_package_relationship field NOT found!<br>";
            $output .= "Please run the SQL command again.<br>";
        }

        // Show all fields for awwards (rowable_id = 16)
        $output .= "<hr>";
        $output .= "<h3>All fields for Awards (rowable_id = 16):</h3>";
        $allFields = DB::table('shuttle_scaffold_interface_rows')
            ->where('rowable_id', 16)
            ->orderBy('ord')
            ->get();

        $output .= "<table border='1' style='border-collapse: collapse;'>";
        $output .= "<tr><th>ID</th><th>Field</th><th>Type</th><th>Display Name</th><th>Edit</th><th>Add</th></tr>";
        foreach ($allFields as $field) {
            $output .= "<tr>";
            $output .= "<td>{$field->id}</td>";
            $output .= "<td>{$field->field}</td>";
            $output .= "<td>{$field->type}</td>";
            $output .= "<td>{$field->display_name}</td>";
            $output .= "<td>" . ($field->edit ? 'Yes' : 'No') . "</td>";
            $output .= "<td>" . ($field->add ? 'Yes' : 'No') . "</td>";
            $output .= "</tr>";
        }
        $output .= "</table>";

        return $output;

    } catch (Exception $e) {
        return "❌ ERROR: " . $e->getMessage();
    }
});

// Check if files exist
Route::get('/check-files', function() {
    $output = "<h2>File Check Results:</h2>";

    // Check if edit_add.blade.php exists
    $editAddFile = resource_path('views/shuttle/awwards/edit_add.blade.php');
    if (file_exists($editAddFile)) {
        $output .= "✅ SUCCESS: edit_add.blade.php exists!<br>";
        $output .= "File size: " . filesize($editAddFile) . " bytes<br>";
        $output .= "Last modified: " . date('Y-m-d H:i:s', filemtime($editAddFile)) . "<br>";
    } else {
        $output .= "❌ ERROR: edit_add.blade.php does NOT exist!<br>";
        $output .= "Expected location: {$editAddFile}<br>";
    }

    $output .= "<hr>";

    // Check if directory exists
    $dir = resource_path('views/shuttle/awwards/');
    if (is_dir($dir)) {
        $output .= "✅ SUCCESS: Directory exists!<br>";
        $output .= "Files in directory:<br>";
        $files = scandir($dir);
        foreach ($files as $file) {
            if ($file != '.' && $file != '..') {
                $output .= "- {$file}<br>";
            }
        }
    } else {
        $output .= "❌ ERROR: Directory does NOT exist!<br>";
        $output .= "Expected directory: {$dir}<br>";
    }

    $output .= "<hr>";

    // Check if we can read the file content
    if (file_exists($editAddFile)) {
        $output .= "<h3>File Content Preview (first 500 characters):</h3>";
        $output .= "<pre>" . htmlspecialchars(substr(file_get_contents($editAddFile), 0, 500)) . "...</pre>";
    }

    return $output;
});

// Update blogs scaffold interface to use custom controller
Route::get('/update-blogs-controller', function() {
    try {
        $updated = DB::table('shuttle_scaffold_interfaces')
            ->where('id', 5)
            ->update([
                'controller' => 'App\\Http\\Controllers\\Shuttle\\BlogsController'
            ]);

        if ($updated) {
            // Also update browse fields for blogs
            DB::table('shuttle_scaffold_interface_rows')
                ->where('rowable_id', 5)
                ->where('field', 'keyword')
                ->update(['browse' => 1]);

            DB::table('shuttle_scaffold_interface_rows')
                ->where('rowable_id', 5)
                ->where('field', 'description')
                ->update(['browse' => 1]);

            return "✅ SUCCESS: Blogs controller and browse fields updated successfully!<br>
                    Now visit: <a href='/clear-caches'>/clear-caches</a> to clear cache, then check <a href='/mypanel/blogs'>/mypanel/blogs</a>";
        } else {
            return "❌ ERROR: Failed to update blogs controller";
        }

    } catch (Exception $e) {
        return "❌ ERROR: " . $e->getMessage();
    }
});

// Update awards video field to support file upload
Route::get('/update-awards-video-field', function() {
    try {
        $updated = DB::table('shuttle_scaffold_interface_rows')
            ->where('rowable_id', 16)
            ->where('field', 'video')
            ->update([
                'type' => 'image',
                'display_name' => 'Video File'
            ]);

        if ($updated) {
            return "✅ SUCCESS: Awards video field updated to support file upload!<br>
                    Now visit: <a href='/clear-caches'>/clear-caches</a> to clear cache, then check <a href='/mypanel/awards'>/mypanel/awards</a>";
        } else {
            return "❌ ERROR: Failed to update awards video field";
        }

    } catch (Exception $e) {
        return "❌ ERROR: " . $e->getMessage();
    }
});

// Debug Google OAuth configuration
Route::get('/debug-google-oauth', function() {
    $output = "<h2>Google OAuth Configuration Debug</h2>";

    $output .= "<h3>Environment Variables:</h3>";
    $output .= "GOOGLE_CLIENT_ID: " . (env('GOOGLE_CLIENT_ID') ? 'SET (' . substr(env('GOOGLE_CLIENT_ID'), 0, 10) . '...)' : 'NOT SET') . "<br>";
    $output .= "GOOGLE_CLIENT_SECRET: " . (env('GOOGLE_CLIENT_SECRET') ? 'SET (' . substr(env('GOOGLE_CLIENT_SECRET'), 0, 10) . '...)' : 'NOT SET') . "<br>";
    $output .= "GOOGLE_REDIRECT_URI: " . (env('GOOGLE_REDIRECT_URI') ?: 'NOT SET') . "<br>";

    $output .= "<h3>Config Values:</h3>";
    $output .= "services.google.client_id: " . (config('services.google.client_id') ? 'SET (' . substr(config('services.google.client_id'), 0, 10) . '...)' : 'NOT SET') . "<br>";
    $output .= "services.google.client_secret: " . (config('services.google.client_secret') ? 'SET (' . substr(config('services.google.client_secret'), 0, 10) . '...)' : 'NOT SET') . "<br>";
    $output .= "services.google.redirect: " . (config('services.google.redirect') ?: 'NOT SET') . "<br>";

    $output .= "<h3>Current URL:</h3>";
    $output .= "Current domain: " . request()->getHost() . "<br>";
    $output .= "Expected callback: " . url('/auth/google/callback') . "<br>";

    $output .= "<h3>Test Links:</h3>";
    $output .= "<a href='/auth/google'>Test Google Login</a><br>";
    $output .= "<a href='/clear-caches'>Clear Caches</a><br>";

    return $output;
});

// Add the missing relationship field to database
Route::get('/add-awward-relationship', function() {
    try {
        // Check if field already exists
        $exists = DB::table('shuttle_scaffold_interface_rows')
            ->where('rowable_id', 16)
            ->where('field', 'awward_hasmany_awward_package_relationship')
            ->exists();

        if ($exists) {
            return "✅ Field already exists!";
        }

        // Insert the new relationship field
        $inserted = DB::table('shuttle_scaffold_interface_rows')->insert([
            'rowable_type' => 'Sina\\Shuttle\\Models\\ScaffoldInterface',
            'rowable_id' => 16,
            'parent_id' => 0,
            'field' => 'awward_hasmany_awward_package_relationship',
            'type' => 'relationship',
            'display_name' => 'awward_packages',
            'required' => 0,
            'browse' => 0,
            'read' => 1,
            'edit' => 1,
            'add' => 1,
            'delete' => 0,
            'details' => json_encode([
                'key' => 'id',
                'type' => 'hasMany',
                'label' => 'title',
                'model' => 'App\\Models\\AwwardPackage',
                'pivot' => null,
                'table' => 'awwards_packages',
                'column' => 'awward_id',
                'taggable' => null,
                'pivot_table' => null
            ]),
            'ord' => 18,
            'last_upd' => 0
        ]);

        if ($inserted) {
            return "✅ SUCCESS: awward_hasmany_awward_package_relationship field added successfully!<br>
                    Now visit: <a href='/clear-caches'>/clear-caches</a> to clear cache, then check <a href='/mypanel/awards/create'>/mypanel/awards/create</a>";
        } else {
            return "❌ ERROR: Failed to insert field";
        }

    } catch (Exception $e) {
        return "❌ ERROR: " . $e->getMessage();
    }
});

// Add this route at the top level, outside of any groups
Route::get('/dirblog/{slug}', function($slug) {
    // Remove any ":1" suffix that might be in the slug
    $cleanSlug = preg_replace('/:1$/', '', $slug);
    
    // Check if the parameter is numeric (ID) or a string (slug)
    if (is_numeric($cleanSlug)) {
        $post = \App\Models\Blog::findOrFail($cleanSlug);
    } else {
        $post = \App\Models\Blog::where('slug', $cleanSlug)->firstOrFail();
    }
    
    return view('blog.show', compact('post'));
})->name('dirblog.show');

// Add a direct route for the blog index page
Route::get('/blog', [App\Http\Controllers\BlogController::class, 'index'])->name('blog.index');

// Test Google OAuth
Route::get('/test-google-login', function() {
    try {
        $output = "<h2>Google OAuth Test</h2>";

        $output .= "<h3>Configuration:</h3>";
        $output .= "Client ID: " . (config('services.google.client_id') ? 'SET' : 'NOT SET') . "<br>";
        $output .= "Client Secret: " . (config('services.google.client_secret') ? 'SET' : 'NOT SET') . "<br>";
        $output .= "Redirect URI: " . config('services.google.redirect') . "<br>";

        $output .= "<h3>Test Links:</h3>";
        $output .= "<a href='/auth/google' target='_blank'>Try Google Login</a><br>";

        return $output;
    } catch (\Exception $e) {
        return "Error: " . $e->getMessage();
    }
});

// Test email sending
Route::get('/test-invoice-email', function() {
    try {
        // Create a test PDF path (you can use an existing invoice)
        $testPdfPath = storage_path('app/invoices/test-invoice.pdf');

        // Create a simple test PDF if it doesn't exist
        if (!file_exists($testPdfPath)) {
            $invoiceDirectory = storage_path('app/invoices');
            if (!file_exists($invoiceDirectory)) {
                mkdir($invoiceDirectory, 0755, true);
            }

            $pdf = \Barryvdh\DomPDF\Facade\Pdf::loadHTML('<h1>Test Invoice</h1><p>This is a test invoice.</p>');
            $pdf->save($testPdfPath);
        }

        // Send test email
        \Illuminate\Support\Facades\Mail::to('<EMAIL>')->send(new \App\Mail\InvoiceEmail(
            $testPdfPath,
            'TEST-001',
            now()->addDays(7)->format('Y-m-d'),
            'Test Customer'
        ));

        return "✅ Test invoice email sent <NAME_EMAIL>!";

    } catch (\Exception $e) {
        return "❌ Email test failed: " . $e->getMessage() . "<br>Trace: " . $e->getTraceAsString();
    }
});

// Search route
Route::get('/search', [SearchController::class, 'index'])->name('search');
