@push('css-vendors2')
<link rel="stylesheet" href="{{route('shuttle.assets', 'css/vendor/dataTables.bootstrap4.min.css')}}" />
<link rel="stylesheet" href="{{route('shuttle.assets', 'css/vendor/datatables.responsive.bootstrap4.min.css')}}" />
<link rel="stylesheet" href="https://cdn.datatables.net/searchpanes/2.0.2/css/searchPanes.dataTables.min.css" />
<link rel="stylesheet" href="https://cdn.datatables.net/select/1.4.0/css/select.dataTables.min.css" />
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.2.3/css/buttons.dataTables.min.css" />

@endpush

<scaffold-interface-filter-modal></scaffold-interface-filter-modal>


            <scaffold-interface-table
                url="{{ route('shuttle.scaffold_interface.datatable', array_merge(['scaffold_interface' => $scaffoldInterface], request()->all())) }}"
                delete-route="{{route('shuttle.scaffold_interface.destroy',['scaffold_interface' => $scaffoldInterface, 'id' => '__id'])}}"
                :columns="{{ json_encode($columns) }}"
            >
            </scaffold-interface-table>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete "<span id="deleteItemTitle"></span>"?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">Delete</button>
            </div>
        </div>
    </div>
</div>
 

@push('js')
<script>
$(document).ready(function() {
    var deleteId = null;

    // Handle delete button click
    $(document).on('click', '.remove-item', function(e) {
        e.preventDefault();
        deleteId = $(this).data('id');
        var title = $(this).data('title') || 'this item';
        $('#deleteItemTitle').text(title);
        $('#deleteModal').modal('show');
    });

    // Handle confirm delete
    $('#confirmDelete').on('click', function() {
        if (deleteId) {
            var deleteUrl = "{{ route('shuttle.scaffold_interface.destroy', ['scaffold_interface' => $scaffoldInterface, 'id' => '__id']) }}".replace('__id', deleteId);

            // Create and submit form
            var form = $('<form method="POST" action="' + deleteUrl + '">' +
                '<input type="hidden" name="_token" value="{{ csrf_token() }}">' +
                '<input type="hidden" name="_method" value="DELETE">' +
                '</form>');

            $('body').append(form);
            form.submit();
        }
    });
});
</script>
@endpush