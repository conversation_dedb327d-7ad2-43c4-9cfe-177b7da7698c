<?php

namespace App\Http\Controllers\Shuttle;

use App\Models\Review;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Sina\Shuttle\Models\ScaffoldInterface;
use Sina\Shuttle\Http\Controllers\ShuttleController;
use Sina\Shuttle\Http\Resources\DataTableResource;

class ReviewsController extends ShuttleController
{
    public function index(ScaffoldInterface $scaffoldInterface, Request $request)
    {
        $view = 'shuttle::scaffold.index';
        if (view()->exists("shuttle.reviews.index")) {
            $view = "shuttle.reviews.index";
        }

        $columns = $this->getDataTableResource(DataTableResource::newInstance()->setScaffoldInterface($scaffoldInterface))->columns();
        $add = false; // Reviews are created from frontend, not admin

        return view($view, compact('columns', 'scaffoldInterface', 'add'));
    }

    public function datatable(Request $request, ScaffoldInterface $scaffoldInterface)
    {
        // Use standard Shuttle datatable with only view and delete actions
        return $this->getDataTableResource(
            DataTableResource::newInstance()
                ->setScaffoldInterface($scaffoldInterface)
                ->addAction(fn ($data) => '<a href="' . route('shuttle.scaffold_interface.show', [$scaffoldInterface, $data->id]) . '" class="btn btn-bootstrap-padding btn-info" title="View"><i class="glyph-icon simple-icon-eye"></i></a>')
                ->addAction(fn ($data) => '<button type="button" class="btn btn-bootstrap-padding btn-danger remove-item" data-id="'.$data->id.'" title="Delete"><i class="glyph-icon simple-icon-trash"></i></button>')
        )->json();
    }
}
