<?php

namespace App\Http\Controllers\Shuttle;

use App\Models\Review;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Sina\Shuttle\Models\ScaffoldInterface;
use Sina\Shuttle\Http\Controllers\ShuttleController;
use Sina\Shuttle\Http\Resources\DataTableResource;

class ReviewsController extends ShuttleController
{
    public function index(ScaffoldInterface $scaffoldInterface, Request $request)
    {
        $view = 'shuttle::scaffold.index';
        if (view()->exists("shuttle.reviews.index")) {
            $view = "shuttle.reviews.index";
        }

        $columns = $this->getDataTableResource(DataTableResource::newInstance()->setScaffoldInterface($scaffoldInterface))->columns();
        $add = false; // Reviews are created from frontend, not admin

        return view($view, compact('columns', 'scaffoldInterface', 'add'));
    }

    public function datatable(ScaffoldInterface $scaffoldInterface, Request $request)
    {
        $query = Review::query();

        // Apply filters if provided
        if ($request->has('section_type') && $request->section_type) {
            $query->where('section_type', $request->section_type);
        }

        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        if ($request->has('rating') && $request->rating) {
            $query->where('rating', $request->rating);
        }

        // Debug: Log query and count
        Log::info('Reviews Query: ' . $query->toSql());
        Log::info('Reviews Count: ' . $query->count());

        return $this->getDataTableResource(
            DataTableResource::newInstance()
                ->setScaffoldInterface($scaffoldInterface)
                ->setQuery($query)
                ->addAction(fn ($data) => '<a href="' . route('shuttle.scaffold_interface.show', [$scaffoldInterface, $data->id]) . '" class="btn btn-bootstrap-padding btn-info" title="View"><i class="glyph-icon simple-icon-eye"></i></a>')
                ->addAction(fn ($data) => '<a href="' . route('shuttle.scaffold_interface.edit', [$scaffoldInterface, $data->id]) . '" class="btn btn-bootstrap-padding btn-primary" title="Edit"><i class="glyph-icon simple-icon-pencil"></i></a>')
                ->addAction(fn ($data) => '<button type="button" class="btn btn-bootstrap-padding btn-danger remove-item" data-id="'.$data->id.'" title="Delete"><i class="glyph-icon simple-icon-trash"></i></button>')
        )->json();
    }
}
