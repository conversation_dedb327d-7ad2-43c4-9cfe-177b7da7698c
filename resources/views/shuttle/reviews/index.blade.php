@extends('shuttle::admin')
@section('breadcrumbs')
    @php
        $breadCrumbs = [
            'Home' => route('shuttle.index'),
            $scaffoldInterface->display_name_plural => route('shuttle.scaffold_interface.index',$scaffoldInterface)
        ]
    @endphp
    <div class="pageTitle">
        <div class="pageTitle-title">
            <h1>{{array_key_last ($breadCrumbs)}}</h1>
        </div>
        <!-- /.pageTitle-title -->
        <div class="pageTitle-down">
            <ul>
                @foreach($breadCrumbs as $bread => $route)
                    <li class="breadcrumb-item @if($loop->last) active @endif">
                        <a href="{{$route}}">{{$bread}}</a>
                    </li>
                @endforeach
            </ul>
        </div>
    </div>
@stop

@section('main')

    <div class="row mb-5">
        <div class="col-md-12">
            <div class="card">
                <form class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <select name="status" id="status-filter">
                                <option value="">All Status</option>
                                <option value="pending">Pending</option>
                                <option value="approved">Approved</option>
                                <option value="rejected">Rejected</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            <button class="btn btn-primary w-100" type="submit">Search</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <scaffold-interface-table
            url="{{ route('shuttle.scaffold_interface.datatable', $scaffoldInterface) }}"
            delete-route="{{route('shuttle.scaffold_interface.destroy',['scaffold_interface' => $scaffoldInterface, 'id' => '__id'])}}"
            :columns="{{ json_encode($columns) }}"></scaffold-interface-table>

@stop
