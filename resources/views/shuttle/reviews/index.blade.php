@extends('shuttle::admin')
@section('breadcrumbs')
    @php
        $breadCrumbs = [
            'Home' => route('shuttle.index'),
            $scaffoldInterface->display_name_plural => route('shuttle.scaffold_interface.index',$scaffoldInterface)
        ]
    @endphp
    <div class="pageTitle">
        <div class="pageTitle-title">
            <h1>{{array_key_last ($breadCrumbs)}}</h1>
        </div>
        <!-- /.pageTitle-title -->
        <div class="pageTitle-down">
            <ul>
                @foreach($breadCrumbs as $bread => $route)
                    <li class="breadcrumb-item @if($loop->last) active @endif">
                        <a href="{{$route}}">{{$bread}}</a>
                    </li>
                @endforeach
            </ul>
        </div>
    </div>
@stop

@section('main')

    <div class="row mb-5">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4>Filter Reviews</h4>
                </div>
                <div class="card-body">
                    <form class="row" method="GET">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Section Type</label>
                                <select name="section_type" id="section-type-filter" class="form-control">
                                    <option value="">All Sections</option>
                                    <option value="award" {{ request('section_type') == 'award' ? 'selected' : '' }}>Awards</option>
                                    <option value="conference" {{ request('section_type') == 'conference' ? 'selected' : '' }}>Conferences</option>
                                    <option value="course" {{ request('section_type') == 'course' ? 'selected' : '' }}>Courses</option>
                                    <option value="festival" {{ request('section_type') == 'festival' ? 'selected' : '' }}>Festivals</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Status</label>
                                <select name="status" id="status-filter" class="form-control">
                                    <option value="">All Status</option>
                                    <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                                    <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>Approved</option>
                                    <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>Rejected</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Rating</label>
                                <select name="rating" id="rating-filter" class="form-control">
                                    <option value="">All Ratings</option>
                                    <option value="5" {{ request('rating') == '5' ? 'selected' : '' }}>5 Stars</option>
                                    <option value="4" {{ request('rating') == '4' ? 'selected' : '' }}>4 Stars</option>
                                    <option value="3" {{ request('rating') == '3' ? 'selected' : '' }}>3 Stars</option>
                                    <option value="2" {{ request('rating') == '2' ? 'selected' : '' }}>2 Stars</option>
                                    <option value="1" {{ request('rating') == '1' ? 'selected' : '' }}>1 Star</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div>
                                    <button class="btn btn-primary" type="submit">Filter</button>
                                    <a href="{{ route('shuttle.scaffold_interface.index', $scaffoldInterface) }}" class="btn btn-secondary">Clear</a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <scaffold-interface-table
            url="{{ route('shuttle.scaffold_interface.datatable', $scaffoldInterface) }}"
            delete-route="{{route('shuttle.scaffold_interface.destroy',['scaffold_interface' => $scaffoldInterface, 'id' => '__id'])}}"
            :columns="{{ json_encode($columns) }}"></scaffold-interface-table>

@stop
