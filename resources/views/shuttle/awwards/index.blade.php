@extends('shuttle::admin')

@section('breadcrumbs')
    @include('shuttle::includes.breadcrumbs', ['breadCrumbs' => ['Home' => route('shuttle.index'),
    $scaffoldInterface->display_name_plural => route('shuttle.scaffold_interface.index',$scaffoldInterface)], 'btn' =>
    ($add) ? route('shuttle.scaffold_interface.create',$scaffoldInterface) : null])
@stop

@section('main')
<div class="row">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-body">
                <table id="awards-table" class="data-table data-table-feature">
                    <thead>
                        <tr>
                            @foreach($columns as $column)
                                <th>{{ $column['title'] }}</th>
                            @endforeach
                            <th>Actions</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
</div>
@endsection

@push('js')
<script>
$(document).ready(function() {
    var table = $('#awards-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: "{{ route('shuttle.scaffold_interface.datatable', array_merge(['scaffold_interface' => $scaffoldInterface], request()->all())) }}",
        columns: [
            @foreach($columns as $column)
                { data: '{{ $column['data'] }}', name: '{{ $column['name'] }}' },
            @endforeach
            { data: 'action', name: 'action', orderable: false, searchable: false }
        ]
    });

    // Handle delete button click
    $(document).on('click', '.remove-item', function(e) {
        e.preventDefault();
        
        if (confirm('Are you sure you want to delete this award?')) {
            var id = $(this).data('id');
            var deleteUrl = "{{ route('shuttle.scaffold_interface.destroy', ['scaffold_interface' => $scaffoldInterface, 'id' => '__id']) }}".replace('__id', id);
            
            $.ajax({
                url: deleteUrl,
                type: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    table.ajax.reload();
                    // Show success message
                    alert('Award deleted successfully!');
                },
                error: function(xhr) {
                    alert('Error deleting award: ' + xhr.responseText);
                }
            });
        }
    });
});
</script>
@endpush
